# 🌐 Lucid 多引擎翻译系统开发任务 (v0.4)

> **功能定位**：页面翻译系统，与现有词典功能完全独立  
> **核心架构**：多引擎 + 降级策略 + 统一接口  
> **参考实现**：immersive-translate 的 DOM 注入模式  
> **命名规范**：lu- 前缀，简洁高效  
> **技术栈**：WXT + TypeScript（复用现有基础设施）

---

## 🎯 核心功能概述

### 多引擎翻译架构

- **引擎抽象**：Google、Microsoft、AI 翻译等统一接口
- **降级策略**：引擎内 API 降级 + 跨引擎降级
- **配置管理**：用户可选择引擎优先级和参数
- **故障恢复**：自动切换和错误处理

### 翻译系统特性

- **段落翻译**：扫描页面可翻译节点（p, h1-h6, li, td 等）
- **DOM 注入**：参考 immersive-translate 结构，使用 lu- 命名规范
- **状态管理**：lu-view="origin|dual|trans" 三态切换
- **样式系统**：独立的翻译样式，支持主题切换
- **交互触发**：快捷键 + 右键菜单

### 架构设计图

```
翻译服务层 (TranslateService)
    ↓
引擎管理器 (EngineManager)
    ↓
引擎抽象层 (TranslateEngine)
    ↓
┌─────────────┬──────────────┬──────────────┐
│ GoogleEngine│ MicrosoftEngine│ AIEngine     │
├─────────────┼──────────────┼──────────────┤
│ API 1 → 2   │ Azure API    │ OpenAI API   │
└─────────────┴──────────────┴──────────────┘
```

---

## 📋 分阶段实现计划

## 阶段 1：引擎架构设计（1 天）

| 任务                 | 实现要点                                                 | 验收标准                     |
| -------------------- | -------------------------------------------------------- | ---------------------------- |
| **1.1 引擎抽象层**   | 创建`TranslateEngine`基类<br>定义统一的翻译接口          | 抽象类设计完善，易于扩展     |
| **1.2 配置管理系统** | 实现`TranslateConfigManager`<br>支持引擎优先级和参数配置 | 配置可持久化，支持运行时修改 |
| **1.3 引擎管理器**   | 创建`EngineManager`<br>统一管理所有翻译引擎              | 引擎注册、选择、降级逻辑正确 |
| **1.4 统一服务层**   | 实现`TranslateService`<br>提供简洁的翻译接口             | 上层调用者无需关心引擎细节   |

### 文件结构

```
src/features/translate/
├── engines/
│   ├── base.ts                 # 抽象引擎基类
│   ├── google.ts               # Google翻译引擎
│   ├── microsoft.ts            # Microsoft翻译引擎
│   ├── ai.ts                   # AI翻译引擎
│   └── index.ts                # 引擎导出
├── config.ts                   # 配置管理
├── engine-manager.ts           # 引擎管理器
├── translate.service.ts        # 统一服务层
├── types.ts                    # 类型定义
└── index.ts                    # 模块导出
```

---

## 阶段 2：Google 引擎实现（1 天）

| 任务                    | 实现要点                                                   | 验收标准                    |
| ----------------------- | ---------------------------------------------------------- | --------------------------- |
| **2.1 Google 引擎基础** | 继承`TranslateEngine`<br>实现 Google 特有的切分逻辑        | 单引擎翻译功能正常          |
| **2.2 多 API 支持**     | 实现 Google API 1 + API 2<br>支持引擎内降级                | API 1 失败自动切换到 API 2  |
| **2.3 分块处理**        | 处理长文本和大批量请求<br>每批次 ≤128 条，单段 ≤5000 字符  | 超长内容自动分块，无丢失    |
| **2.4 Background 代理** | 扩展`background.ts`消息处理<br>新增`TRANSLATE_REQUEST`类型 | Content script 获取翻译结果 |



```typescript
export class GoogleTranslateEngine extends TranslateEngine {
  readonly name = "google";
  readonly maxChunkSize = 5000;
  readonly maxBatchSize = 128;

  private apis: ApiEndpoint[] = [
    {
      name: "google-api-1",
      url: "https://translate-pa.googleapis.com/v1/translateHtml",
    },
    { name: "google-api-2", url: "to-be-provided" },
  ];

  async translateBatch(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]>;
  private async translateChunkWithFallback(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]>;
}
```

---

## 阶段 3：DOM 注入系统（1 天）

| 任务                 | 实现要点                                                          | 验收标准                |
| -------------------- | ----------------------------------------------------------------- | ----------------------- |
| **3.1 节点扫描器**   | 识别可翻译节点：`p, h1-h6, li, td`<br>排除：`code, pre, .lu-skip` | 控制台输出扫描统计      |
| **3.2 DOM 注入机制** | 参考 immersive-translate 结构<br>使用 lu-wrapper/lu-block 模式    | 译文正确显示在原文下方  |
| **3.3 状态管理**     | 实现`lu-view`属性控制<br>origin/dual/trans 三态切换               | 状态切换时显示/隐藏正确 |
| **3.4 样式注入**     | 独立的 translate.css 样式系统<br>支持深色主题和弱化效果           | 译文样式与主题协调      |

### DOM 结构设计（参考 immersive-translate）

```html
<p>
  原文内容
  <font class="notranslate lu-wrapper" lang="zh-CN">
    <br />
    <font class="notranslate lu-block lu-weak">译文内容</font>
  </font>
</p>
```

### 文件结构

```
src/content/
└── translate-manager.ts    # 翻译DOM管理器

src/styles/
└── translate.css          # 独立翻译样式系统
```

---

## 阶段 4：交互控制系统（0.5 天）

| 任务               | 实现要点                                           | 验收标准             |
| ------------------ | -------------------------------------------------- | -------------------- |
| **4.1 快捷键支持** | 利用现有 wxt.config.ts 配置<br>添加 Alt+T 翻译触发 | Alt+T 触发页面翻译   |
| **4.2 右键菜单**   | 扩展现有 background.ts 菜单<br>添加"翻译页面"选项  | 右键菜单显示翻译选项 |
| **4.3 状态切换**   | 实现三态切换逻辑<br>origin ↔ dual ↔ trans          | 多次按键正确切换状态 |

---

## 阶段 5：样式与优化（0.5 天）

| 任务             | 实现要点                                       | 验收标准               |
| ---------------- | ---------------------------------------------- | ---------------------- |
| **5.1 样式完善** | 实现 lu-命名规范的 CSS<br>响应式布局和动画效果 | 译文显示美观，布局稳定 |
| **5.2 性能优化** | 批量处理优化<br>DOM 操作防抖和缓存             | 大页面翻译流畅         |
| **5.3 错误处理** | API 失败回退机制<br>用户友好的错误提示         | 网络异常时优雅降级     |

---

## 🛠️ 核心技术架构

### 1. 引擎抽象层设计

```typescript
// src/features/translate/engines/base.ts
export abstract class TranslateEngine {
  abstract readonly name: string;
  abstract readonly maxChunkSize: number;
  abstract readonly maxBatchSize: number;

  abstract translateBatch(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]>;
  abstract getAvailableApis(): ApiEndpoint[];
  abstract selectApi(): ApiEndpoint;
}
```

### 2. 引擎管理器设计

```typescript
// src/features/translate/engine-manager.ts
export class TranslateEngineManager {
  private engines: Map<string, TranslateEngine> = new Map();
  private config: TranslateConfig;

  async translate(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]> {
    const enginePriority = this.config.getEnginePriority();

    for (const engineName of enginePriority) {
      try {
        const engine = this.engines.get(engineName);
        return await engine.translateBatch(texts, options);
      } catch (error) {
        console.warn(`Engine ${engineName} failed, trying next...`);
        continue;
      }
    }

    throw new Error("All translation engines failed");
  }
}
```

### 3. 统一服务层设计

```typescript
// src/features/translate/translate.service.ts
export class TranslateService {
  private engineManager: TranslateEngineManager;

  async translateTexts(
    texts: string[],
    options: TranslateOptions = {}
  ): Promise<string[]> {
    return await this.engineManager.translate(texts, options);
  }

  async translatePage(targetLanguage: string = "zh"): Promise<void> {
    const translatableNodes = this.scanTranslatableNodes();
    const texts = translatableNodes.map((node) => node.textContent);
    const translations = await this.translateTexts(texts, {
      to: targetLanguage,
    });
    this.injectTranslations(translatableNodes, translations);
  }
}
```

### 4. 配置系统设计

```typescript
// src/features/translate/config.ts
export interface TranslateConfig {
  primaryEngine: string;
  enginePriority: string[];
  enableFallback: boolean;

  engines: {
    google: {
      apiEndpoints: ApiEndpoint[];
      timeout: number;
      retryCount: number;
    };
    microsoft: MicrosoftEngineConfig;
    ai: AIEngineConfig;
  };
}
```

### 5. DOM 注入器设计

```typescript
// src/content/translate-manager.ts
class TranslateManager {
  scanTranslatableNodes(): HTMLElement[];
  injectTranslation(node: HTMLElement, translation: string): void;
  setViewMode(mode: "origin" | "dual" | "trans"): void;
}
```

### 6. 样式系统（lu-命名规范）

```css
/* src/styles/translate.css */
:root {
  --lu-trans-opacity: 0.618;
  --lu-trans-color: #6b7280;
}

[lu-view="origin"] .lu-wrapper {
  display: none !important;
}
[lu-view="trans"] :not(.lu-wrapper) > * {
  display: none !important;
}

.lu-wrapper {
  word-break: break-word;
  user-select: text;
}
.lu-block {
  color: var(--lu-trans-color);
}
.lu-weak {
  opacity: var(--lu-trans-opacity);
}
```

---

## 🎯 验收标准

### 架构验收

- [ ] 引擎抽象：Google、Microsoft、AI 引擎实现统一接口
- [ ] 降级策略：引擎内 API 降级 + 跨引擎降级正常工作
- [ ] 配置管理：用户可修改引擎优先级，配置持久化
- [ ] 服务层统一：上层调用无需关心具体引擎实现

### 功能验收

- [ ] 页面扫描：正确识别可翻译节点，排除代码块
- [ ] 批量翻译：5 段文本<2 秒完成翻译
- [ ] DOM 注入：译文正确插入，不影响原有布局
- [ ] 状态切换：Alt+T 多次按下正确切换三态
- [ ] 样式协调：深色主题下译文颜色自适应

### 性能标准

- [ ] 大页面支持：100+段落翻译不卡顿
- [ ] 内存控制：长时间使用无明显内存泄漏
- [ ] 错误恢复：API 异常时优雅降级到备用引擎
- [ ] 兼容性：不与现有功能（高亮、词典）冲突

---

## 🧪 测试用例

### 多引擎降级测试

```typescript
// 测试引擎降级策略
async function testEngineFallback() {
  // 1. 配置引擎优先级
  translateService.setEnginePriority(["google", "microsoft", "ai"]);

  // 2. 模拟Google引擎失败
  mockGoogleEngine.setFailure(true);

  // 3. 执行翻译，应该自动切换到Microsoft
  const result = await translateService.translateTexts(["Hello world"]);
  assert(result[0] === "你好世界", "Should fallback to Microsoft engine");
}
```

### Google 引擎内 API 降级测试

```typescript
// 测试Google引擎内部API降级
async function testGoogleApiFallback() {
  const googleEngine = new GoogleTranslateEngine();

  // 模拟API 1失败
  mockGoogleApi1.setFailure(true);

  // 执行翻译，应该切换到API 2
  const result = await googleEngine.translateBatch(["Hello"]);
  assert(result[0] === "你好", "Should fallback to Google API 2");
}
```

### 基础功能测试

```html
<!-- 创建测试页面 -->
<div id="test-content">
  <h1>Test Title</h1>
  <p>This is a test paragraph for translation.</p>
  <li>List item content</li>
  <code>// 这段代码不应该被翻译</code>
</div>
```

### 配置系统测试

```typescript
// 验证配置系统
async function testConfiguration() {
  const config = new TranslateConfigManager();

  // 1. 测试引擎优先级设置
  config.setEnginePriority(["microsoft", "google", "ai"]);
  assert(config.getEnginePriority()[0] === "microsoft");

  // 2. 测试配置持久化
  await config.saveConfig();
  const reloadedConfig = new TranslateConfigManager();
  assert(reloadedConfig.getEnginePriority()[0] === "microsoft");
}
```

---

## 🚀 后续扩展方向

### v0.5 增强功能

- **DeepL 引擎**：高质量翻译引擎集成
- **Azure Translator**：企业级翻译服务
- **智能检测**：自动识别页面语言
- **自定义过滤**：用户定义翻译范围
- **翻译缓存**：相同内容复用翻译结果

### v0.6 高级功能

- **实时翻译**：页面动态内容自动翻译
- **专业术语**：领域词典集成
- **翻译质量**：多引擎结果对比和评分
- **离线模式**：本地翻译模型支持
- **API 使用统计**：监控各引擎使用情况和成功率

### v0.7 企业功能

- **自定义引擎**：支持用户自定义翻译引擎
- **成本优化**：根据成本和质量智能选择引擎
- **批量管理**：大规模文档翻译工作流
- **团队协作**：翻译结果共享和协作编辑

---

_多引擎翻译系统，支持降级策略，确保翻译服务的高可用性_ 🌍
