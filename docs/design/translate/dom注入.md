### Lucid 翻译注入命名规范 (v0.1)

| 目标     | 做法                                 | 说明                                             |          |                                             |
| -------- | ------------------------------------ | ------------------------------------------------ | -------- | ------------------------------------------- |
| 前缀统一 | **`lu-`** （简短、避免与第三方冲突） | 避免 `lucid-…` 过长；如需更显眼可切换全称        |          |                                             |
| 状态开关 | 根节点自定义 attr：\`lu-view="origin | dual                                             | trans"\` | 单语 / 双语 3 态；Tailwind/CSS 只看一个属性 |
| 组件粒度 | 段落级 Wrapper + 内层 Block          | 与 Immersive Translate 相同 DOM 深度，改类名即可 |          |                                             |

---

## 1 · DOM 结构（双语模式示例）

```html
<p>
  Learn about configuring <PERSON> …
  <font class="notranslate lu-wrapper" lang="zh-CN">
    <br />
    <font class="notranslate lu-block lu-weak"
      >了解如何通过 Google Vertex AI 配置 Claude Code …</font
    >
  </font>
</p>
```

| 类名         | 作用                              |
| ------------ | --------------------------------- |
| `lu-wrapper` | 外层容器，阻断继承样式 & 标记语言 |
| `lu-block`   | 译文块（行内或块级由 CSS 控）     |
| `lu-weak`    | 弱化透明度，可根据主题变量调整    |

---

## 2 · JS 注入片段

```ts
function injectLuTranslation(node: HTMLElement, zh: string) {
  const wrap = document.createElement("font");
  wrap.className = "notranslate lu-wrapper";
  wrap.lang = "zh-CN";

  const br = document.createElement("br");
  const block = document.createElement("font");
  block.className = "notranslate lu-block lu-weak";
  block.textContent = zh;

  wrap.append(br, block);
  node.appendChild(wrap);
}
```

---

## 3 · 设计 Token & Tailwind 配置

### 3.1 `design-tokens.css`

```css
:root {
  --lu-origin-color: #111;
  --lu-trans-color: #6b7280;
  --lu-trans-opacity: 0.62; /* 黄金分割弱化 */
}

html.dark {
  --lu-origin-color: #eee;
  --lu-trans-color: #9ca3af;
}
```

### 3.2 `tailwind.config.ts`

```ts
import { defineConfig } from "tailwindcss";

export default defineConfig({
  content: ["entrypoints/**/*.{ts,tsx,html}"],
  theme: {
    extend: {
      colors: {
        luOrigin: "var(--lu-origin-color)",
        luTrans: "var(--lu-trans-color)",
      },
      opacity: {
        lu: "var(--lu-trans-opacity)",
      },
    },
  },
});
```

---

## 4 · 基础样式（可直接写在 `tailwind.css`）

```css
/* 显隐控制：根 attr lu-view */
[lu-view="origin"] .lu-wrapper {
  display: none !important;
}
[lu-view="trans"] :not(.lu-wrapper) > * {
  display: none !important;
}

.lu-wrapper {
  word-break: break-word;
  user-select: text;
}
.lu-block {
  color: var(--lu-trans-color);
}
.lu-weak {
  opacity: var(--lu-trans-opacity);
}
```

---

## 5 · 状态切换 API（示例）

```ts
export function setLuView(mode: "origin" | "dual" | "trans") {
  document.documentElement.setAttribute("lu-view", mode);
}
```

- **快捷键** Alt+E → `dual ↔ origin`
- 浮动面板可供三态切换

---

## 6 · 集成 Checklist

- [ ] Tailwind 已扫描 `entrypoints/` & `design-tokens.css`
- [ ] `lu-view` 默认为 `origin`，翻译完切 `dual`
- [ ] 所有插入元素均加 `class="notranslate"`，避免二次翻译
- [ ] 调试深色模式：确认色 token 随 `html.dark` 变化

---

随时告诉我需要补充的 **具体代码** 或 **测试用例**！

## 1 · 整体思路

这段 HTML 来自 **Immersive Translate** 类浏览器插件的「**双语对照**」注入策略。核心做法是 **在原段落内部追加一棵“翻译子树”** ，借助语义 class / attribute 与少量全局 CSS 实现显隐、弱化和排版，而不改动原文节点。

```
<p>
  原文 …
  <font class="notranslate immersive-translate-target-wrapper" lang="zh-CN">
    <br>
    <font class="…-translation-block-wrapper"> 译文 … </font>
  </font>
</p>
```

---

## 2 · DOM 注入细节

| 步骤                 | 说明                                                                                                                                                   | 关键点                                                                |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------- |
| ① **扫描可译节点**   | Content-script 以 `querySelectorAll('p, li, h1…, td')` 遍历，排除 `code`, `pre`, `math` 等                                                             | 保证段落粒度；避免破坏语义标签                                        |
| ② **生成译文**       | 将段落文本送翻译引擎；拿到译文字符串                                                                                                                   | 可以合并相邻短节点一次请求                                            |
| ③ **构造“翻译子树”** | - 外层 `<font lang="zh-CN" class="notranslate …-wrapper">`<br>- 插入一个 `<br>` 断行<br>- 内层 `<font class="…-translation-block-wrapper">译文</font>` | _`notranslate`_ 阻止再被浏览器/第三方翻译；`lang` 供朗读/拼写工具识别 |
| ④ **挂载到原节点**   | 直接 `appendChild(wrapper)` 或 `insertAdjacentElement('beforeend', wrapper)`                                                                           | 不替换文本 → 最小化 reflow，保留原 SEO/DOM 结构                       |
| ⑤ **状态标记**       | 在 `<html>` 或 `<body>` 上写 `imt-state="dual"`                                                                                                        | 供 CSS 切换「隐藏 / 单语 / 双语」三态                                 |

---

## 3 · CSS 机制

| 选择器片段                                                                   | 作用                 | 典型规则                                   |
| ---------------------------------------------------------------------------- | -------------------- | ------------------------------------------ |
| `[imt-state="dual"] .immersive-translate-target-translation-block-wrapper`   | 双语模式下显示译文块 | `display:inline-block; margin:8px 0;`      |
| `[imt-state="dual"] .immersive-translate-target-translation-theme-weakening` | 降低译文对比度       | `opacity:0.618;` (黄金比例)                |
| `.immersive-translate-target-wrapper`                                        | 基础排版             | `word-break:break-word; user-select:text;` |
| `.prose …`（Tailwind Prose）                                                 | 与站点原主题一致     | 字号、行高、颜色变量                       |

> **优势**：
> • 无需重算外层布局，只会扩高所在段落
> • `notranslate` + `lang` 兼容各类阅览/朗读插件
> • 用 attribute 控制「单语/双语」开关，不走 JS 再遍历
>
> **代价**：
> • 依赖 `<font>`（非语义标签）防止原样式污染，但会让 DOM 失真
> • 极长段落直接追加译文，可能导致可访问性朗读重复

---

## 4 · 与我们规划（v3.0）的对应关系

| 模块             | 现有方案里体现             | 我们 v3.0 计划                                       |        |           |
| ---------------- | -------------------------- | ---------------------------------------------------- | ------ | --------- |
| **译文挂载策略** | _同段落内 append + `<br>`_ | 保持一致，或可选「并排 flex」                        |        |           |
| **弱化效果**     | `opacity:0.618`            | 配置化：`--tt-trans-opacity`                         |        |           |
| **状态切换**     | `imt-state` attr           | \`\<html tt-view="dual                               | origin | trans">\` |
| **排版冲突隔离** | 直接用全局 class           | v3.0 提供 **ShadowRoot UI** 备选，避免 Tailwind 冲突 |        |           |
| **可控性**       | 通过 JS 改 `imt-state`     | 浮动控制面板 ➜ 切换 CSS attr                         |        |           |

---

## 5 · 可复用的实现片段

```ts
function injectTranslation(p: HTMLElement, zh: string) {
  const wrapper = document.createElement("font");
  wrapper.className = "notranslate immersive-translate-target-wrapper";
  wrapper.lang = "zh-CN";

  const br = document.createElement("br");
  const inner = document.createElement("font");
  inner.className =
    "notranslate immersive-translate-target-translation-theme-weakening " +
    "immersive-translate-target-translation-block-wrapper";
  inner.textContent = zh;

  wrapper.appendChild(br);
  wrapper.appendChild(inner);
  p.appendChild(wrapper);
}
```

**CSS Token（Tailwind）**

```css
/* design-tokens.css */
:root {
  --tt-trans-opacity: 0.62;
}
[tt-view="dual"] .tt-translation {
  opacity: var(--tt-trans-opacity);
}
```

---

### 小结

- **核心思路**：保持原文节点 → 插入译文子树 → `notranslate` + `lang` 标记 → 全局 CSS 控制显隐与弱化
- **对 v3.0 的启发**：直接复用该 DOM 模型即可与现有站点 Prose/Tailwind 风格完美融合；若担心样式冲突，可在 _ShadowRoot_ 模式下做一份纯 flex 布局版本，供设置切换。
