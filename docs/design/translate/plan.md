# 📦 浏览器网页翻译插件任务计划文档 v2.1（双语对照增强版）

## 一、项目背景与目标

### 背景

用户希望开发一款浏览器插件，支持**通过快捷键或操作触发**，实现对当前网页内容的**双语对照翻译**，以提供比现有方案（如 Edge 内置翻译、Google Translate 插件等）更好的阅读体验。

### 核心目标

- 实现**双语对照显示**而非简单替换，让用户同时看到原文和译文
- 支持多翻译引擎（微软 Edge 翻译服务 + Google Translate API）
- 智能布局算法，根据内容类型自动选择最佳展示方式
- 基于 Manifest V3 架构开发，支持 Chrome / Edge 浏览器

## 二、功能清单与实现计划

### ✅ 核心功能（MVP）

| 功能项           | 说明                                                       |
| ---------------- | ---------------------------------------------------------- |
| **双语对照模式** | 智能判断文本类型，实现并排/并列/悬浮等多种展示方式         |
| **多引擎支持**   | 默认微软翻译，支持切换至 Google Translate，自动故障转移    |
| **智能布局**     | 短文本并排显示、段落上下对照、标题悬浮提示、表格双语单元格 |
| **快捷键触发**   | Alt+E 翻译/还原 , 右侧悬浮面板 可以切换引擎                |
| **视觉优化**     | 译文灰色淡化、未来可调节透明度和字体大小                   |
| **智能排除**     | 自动识别并跳过代码块、数学公式、技术标识符等不需翻译的内容 |
| **缓存管理**     | 双层缓存（内存+IndexedDB），支持离线查看已翻译内容         |
| **批量优化**     | 智能合并相邻短文本，减少 API 请求次数                      |

### 🔜 进阶功能（Phase 2）

| 功能项           | 描述                                                      |
| ---------------- | --------------------------------------------------------- |
| **浮动控制面板** | 可拖拽的迷你控制栏，实时切换翻译，切换引擎， 打开翻译设置 |
| **术语词典**     | 用户自定义专业术语对照表，优先使用词典翻译                |
| **框选翻译**     | 鼠标框选任意区域进行局部翻译                              |

### 🚀 高级功能（Phase 3）

| 功能项           | 描述                                                     |
| ---------------- | -------------------------------------------------------- |
| **悬停翻译**     | 鼠标悬停在段落上，按快捷键（如 Alt+H）即时翻译当前自然段 |
| **智能段落识别** | 自动识别语义完整的段落边界，支持跨元素的段落翻译         |
| **上下文感知**   | 根据前后文优化翻译结果，保持术语一致性                   |
| **实时预览**     | 悬停时显示翻译预览气泡，确认后再进行完整翻译             |
| **PDF 支持**     | 内嵌 PDF 文档的双语对照翻译                              |
| **导出功能**     | 一键导出双语对照的 HTML/PDF 文件                         |
| **团队协作**     | 分享翻译配置、术语库，支持团队统一翻译标准               |

## 三、技术方案设计

### 📡 API 集成方案

#### 主引擎：Microsoft Edge Translate

- JWT 获取：`GET https://edge.microsoft.com/translate/auth`
- 翻译接口：`POST https://api-edge.cognitive.microsofttranslator.com/translate`
- 限制：单次 ≤5000 字符，≤20 段落
- Token 刷新：过期前 60s 自动刷新

#### 备选引擎：Google Translate

- 接口：`POST https://translate-pa.googleapis.com/v1/translateHtml`
- API Key：`AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520`
- 优势：支持 HTML 标签保留，翻译质量稳定

### 🎯 智能排除规则

| 元素类型       | 排除策略 | 识别方法                                         |
| -------------- | -------- | ------------------------------------------------ |
| **代码块**     | 完全跳过 | `<pre>`, `<code>`, `.highlight`, `.prism` 等类名 |
| **数学公式**   | 完全跳过 | MathJax, KaTeX 容器，`$...$`, `$$...$$` 标记     |
| **技术标识**   | 智能识别 | 变量名、函数名、文件路径、URL、命令行            |
| **UI 元素**    | 可配置   | 按钮文本 < 5 字符、纯数字、版本号                |
| **特殊属性**   | 标记跳过 | `data-no-translate`, `translate="no"` 属性       |
| **用户自定义** | 规则匹配 | CSS 选择器、正则表达式、域名规则                 |

### 🎨 双语显示策略

| 内容类型              | 显示方式               | 适用场景                   |
| --------------------- | ---------------------- | -------------------------- |
| **短文本** (<20 字符) | 并排显示 `原文 (译文)` | 按钮、标签、菜单项         |
| **标题** (h1-h6)      | 悬浮提示 + 下标注      | 保持页面结构，鼠标悬停查看 |
| **段落**              | 上下并列，可折叠       | 正文内容，支持对比阅读     |
| **列表项**            | 缩进对照               | 保持列表层级关系           |
| **表格**              | 单元格内双语           | 数据对比，不破坏表格布局   |
| **代码注释**          | 行内对照               | 仅翻译注释部分             |

### 🏗️ 架构设计

```
[用户交互层]
    ├── 快捷键监听
    ├── 右键菜单
    ├── 悬停检测器 (Phase 3)
    └── 浮动面板
         ↓
[Content Script]
    ├── DOM 分析器 (识别可翻译内容)
    ├── 排除规则引擎 (过滤不需翻译元素)
    ├── 布局引擎 (智能选择展示方式)
    ├── 段落识别器 (Phase 3)
    └── 样式注入器 (动态 CSS)
         ↓
[Service Worker]
    ├── 翻译调度器 (请求合并、优先级)
    ├── 引擎管理器 (多引擎切换、故障转移)
    ├── 缓存管理器 (LRU 策略)
    └── 配置同步器
         ↓
[存储层]
    ├── chrome.storage.local (用户配置、排除规则)
    ├── IndexedDB (翻译缓存)
    └── Memory Cache (热数据)
```

## 四、开发阶段计划

| 时间          | 阶段     | 主要任务                                                               |
| ------------- | -------- | ---------------------------------------------------------------------- |
| **第 1 周**   | 技术预研 | • 验证双引擎 API 可用性<br>• 设计双语 DOM 结构<br>• 测试排除规则准确性 |
| **第 2-3 周** | 核心开发 | • 实现翻译引擎抽象层<br>• 开发智能排除规则引擎<br>• 完成基础双语渲染   |
| **第 4 周**   | 交互优化 | • 快捷键系统<br>• 进度反馈 UI<br>• 显示模式切换                        |
| **第 5 周**   | 性能优化 | • 请求合并算法<br>• 虚拟滚动支持<br>• 缓存策略优化                     |
| **第 6 周**   | 测试完善 | • 主流网站兼容性测试<br>• 技术网站排除规则验证<br>• 用户体验优化       |

## 五、关键技术挑战

### 🚧 技术难点

| 挑战               | 解决方案                                     |
| ------------------ | -------------------------------------------- |
| **代码识别准确性** | 多重检测：标签 + 类名 + 内容特征 + 上下文    |
| **公式保护**       | 预处理阶段标记所有公式容器，建立禁翻译区域图 |
| **混合内容处理**   | 对段落进行分词，仅翻译自然语言部分           |
| **性能影响**       | 使用 WeakMap 缓存已分析节点，避免重复计算    |
| **规则冲突**       | 优先级系统：用户规则 > 默认规则 > 智能识别   |

### ⚠️ 风险控制

| 风险点           | 缓解措施                       |
| ---------------- | ------------------------------ |
| **误排除内容**   | 提供快速覆盖机制，右键强制翻译 |
| **规则过于宽松** | 默认保守策略，用户可调节灵敏度 |
| **技术内容误翻** | 建立技术术语白名单，保持原文   |
| **性能退化**     | 规则缓存 + 批量处理 + 异步执行 |

## 六、用户体验设计

### 🎯 交互原则

- **智能识别**：自动识别技术内容，减少用户配置
- **可控性**：用户可随时调整排除规则和翻译范围
- **透明度**：通过视觉提示展示哪些内容被排除
- **灵活性**：支持临时覆盖自动规则

### 📊 性能指标

- 排除规则匹配：< 10ms/页面
- 首次翻译响应：< 2 秒
- 增量翻译延迟：< 500ms
- 规则准确率：> 95%

## 七、典型场景优化

### 📚 技术文档站点

- 自动识别代码示例、API 参考、命令行
- 仅翻译说明文字，保留所有技术标识
- 支持 GitHub、MDN、Stack Overflow 等站点优化

### 📐 学术论文

- 保护数学公式、引用标记、图表标号
- 识别并保留专业术语
- 支持 arXiv、IEEE、ACM 等平台

### 💻 开发者博客

- 智能区分正文和代码片段
- 保留内联代码样式
- 支持主流博客平台和静态站点

---

**下一步行动**：优先实现核心排除规则引擎，确保技术内容的准确识别和保护。
