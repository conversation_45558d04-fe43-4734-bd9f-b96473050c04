# 翻译系统架构与流程分析

> 本文档详细分析翻译系统中各个模块之间的调用关系、数据流转、并行处理机制，以及新旧版本的对比。

## 🏗️ 系统架构概览

### 整体架构图

```
entrypoints/content.ts (主入口)
    ↓
TranslateManagerAdapter (适配器层)
    ↓
TranslationOrchestrator (编排器核心)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   DomScanner    │ TranslateService │   DomRenderer   │
│   (扫描层)      │   (翻译层)      │   (渲染层)      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 详细流程图

```mermaid
flowchart TD
    %% 入口层
    A[用户触发翻译] --> B[entrypoints/content.ts]
    B --> C[TranslateManagerAdapter.translatePage]

    %% 编排器初始化
    C --> D[TranslationOrchestrator 创建]
    D --> E[初始化组件]
    E --> F[DomScanner]
    E --> G[TranslateService]
    E --> H[DomRenderer]
    E --> I[LifecycleManager]
    E --> J[StateManager]
    E --> K[EventBus]

    %% 翻译流程开始
    C --> L[orchestrator.translatePage]
    L --> M{stateManager.canStart?}
    M -->|No| N[抛出错误]
    M -->|Yes| O[setState: SCANNING]

    %% 预检查阶段
    O --> P[performPreflightCheck]
    P --> Q[性能监测]
    P --> R[内存检查]

    %% 扫描阶段
    Q --> S[scanPage]
    R --> S
    S --> T[DomScanner.scan]

    %% 扫描详细流程
    T --> U[elementAnalyzer.analyzeElements]
    U --> V[遍历DOM树]
    V --> W[识别可翻译元素]
    W --> X[convertToScannedNode]
    X --> Y[applyFilters - 排除规则]
    Y --> Z[filterParentChildConflicts - 冲突处理]
    Z --> AA[prioritizeNodes - 优先级排序]

    AA --> BB{nodes.length > 0?}
    BB -->|No| CC[返回空结果]
    BB -->|Yes| DD[setState: TRANSLATING]

    %% 流程选择
    DD --> EE{enableLazyLoading && nodes > 50?}
    EE -->|Yes| FF[executeLazyTranslation - 大页面]
    EE -->|No| GG[executeBatchTranslation - 小页面]

    %% 大页面懒加载流程
    FF --> HH[categorizeElementsByVisibility]
    HH --> II[visibleElements - 可见元素]
    HH --> JJ[invisibleElements - 不可见元素]
    II --> KK[优先处理可见元素]
    JJ --> LL[LazyLoadManager.observeElements]

    %% 小页面批量流程
    GG --> MM[setState: RENDERING]
    KK --> MM

    %% 翻译循环
    MM --> NN[遍历每个节点]
    NN --> OO{被中止?}
    OO -->|Yes| PP[中断处理]
    OO -->|No| QQ[translateText]

    %% 翻译服务详细流程
    QQ --> RR[TranslateService.translateText]
    RR --> SS[translateTexts 数组处理]
    SS --> TT[输入验证]
    TT --> UU[强制纯文本格式]
    UU --> VV[ensurePlainText 文本清理]
    VV --> WW[缓存检查 translationCache.get]

    WW --> XX{有缓存?}
    XX -->|Yes| YY[返回缓存结果]
    XX -->|No| ZZ[TranslateEngineManager.translate]

    %% 引擎管理流程
    ZZ --> AAA[getEnginesByPriority 获取引擎优先级]
    AAA --> BBB[遍历引擎列表]
    BBB --> CCC{尝试翻译}
    CCC -->|成功| DDD[返回翻译结果]
    CCC -->|失败| EEE{启用降级?}
    EEE -->|Yes| FFF[尝试下一个引擎]
    EEE -->|No| GGG[抛出错误]
    FFF --> CCC

    %% Google翻译引擎详细
    DDD --> HHH[GoogleTranslateEngine.translateBatch]
    HHH --> III[buildGoogleApi1Request 构建请求]
    III --> JJJ[HTTP请求 Google API]
    JJJ --> KKK[parseGoogleApi1Response 解析响应]
    KKK --> LLL[缓存翻译结果 translationCache.set]

    %% 渲染阶段
    YY --> MMM[DomRenderer.render]
    LLL --> MMM
    MMM --> NNN[智能注入策略选择]
    NNN --> OOO[InjectionRuleEngine.selectStrategy]
    OOO --> PPP{策略类型}
    PPP -->|inline| QQQ[行内注入 - 短文本链接]
    PPP -->|block| RRR[块级注入 - 段落长文本]
    PPP -->|beside| SSS[侧边注入 - 标题元素]
    PPP -->|hidden-preserve| TTT[隐藏保持 - 隐藏内容]
    PPP -->|skip| UUU[跳过翻译 - 代码元素]

    %% 注入器选择
    QQQ --> VVV[选择注入器]
    RRR --> VVV
    SSS --> VVV
    TTT --> VVV
    UUU --> WWW[跳过渲染]

    VVV --> XXX{智能注入启用?}
    XXX -->|Yes| YYY[EnhancedDOMInjector]
    XXX -->|No| ZZZ[DOMInjector]

    %% 内容处理
    YYY --> AAAA[processContent]
    ZZZ --> AAAA
    AAAA --> BBBB{format类型}
    BBBB -->|html| CCCC[processHtmlContent + 重建链接]
    BBBB -->|text| DDDD[processTextContent]

    %% 注入执行
    CCCC --> EEEE[injector.injectTranslation]
    DDDD --> EEEE
    EEEE --> FFFF[DOM注入执行]
    FFFF --> GGGG[更新元素显示]

    %% 进度报告
    GGGG --> HHHH[reportProgress]
    WWW --> HHHH
    HHHH --> IIII[EventBus.emit progress事件]
    IIII --> JJJJ[更新翻译统计]

    %% 循环控制
    JJJJ --> KKKK{还有节点?}
    KKKK -->|Yes| NN
    KKKK -->|No| LLLL[所有节点处理完成]

    %% 视图切换
    LLLL --> MMMM[viewController.setViewMode DUAL]
    MMMM --> NNNN[setState: COMPLETED]

    %% 结果统计
    NNNN --> OOOO[统计翻译结果]
    OOOO --> PPPP[successful: 成功数量]
    OOOO --> QQQQ[failed: 失败数量]
    OOOO --> RRRR[results: 详细结果]
    OOOO --> SSSS[totalDuration: 总耗时]

    %% 并发控制器 (LifecycleManager)
    I --> TTTT[TranslationConcurrencyController]
    TTTT --> UUUU[maxConcurrent: 8]
    TTTT --> VVVV[rateLimitPerSecond: 8]

    I --> WWWW[LazyLoadManager]
    WWWW --> XXXX[IntersectionObserver]
    XXXX --> YYYY[批量处理可见元素]

    I --> ZZZZ[PerformanceOptimizer]
    ZZZZ --> AAAAA[内存监控]
    ZZZZ --> BBBBB[性能优化]

    %% 错误处理
    N --> CCCCC[用户友好错误提示]
    GGG --> CCCCC
    PP --> CCCCC
    CCCCC --> DDDDD[setState: ERROR]
    DDDDD --> EEEEE[EventBus.emit error事件]

    %% 状态管理流转
    J --> FFFFF[状态流转监控]
    FFFFF --> GGGGG[IDLE → SCANNING → TRANSLATING → RENDERING → COMPLETED]

    %% 样式类
    classDef entryPoint fill:#e1f5fe
    classDef orchestrator fill:#f3e5f5
    classDef scanner fill:#e8f5e8
    classDef translator fill:#fff3e0
    classDef renderer fill:#fce4ec
    classDef lifecycle fill:#f1f8e9
    classDef error fill:#ffebee

    class A,B,C entryPoint
    class D,L,M,O,P,Q,R,S,DD,EE,MM,NNNN orchestrator
    class T,U,V,W,X,Y,Z,AA,BB scanner
    class RR,SS,TT,UU,VV,WW,XX,YY,ZZ,AAA,BBB,CCC,DDD,EEE,FFF,GGG,HHH,III,JJJ,KKK,LLL translator
    class MMM,NNN,OOO,PPP,QQQ,RRR,SSS,TTT,UUU,VVV,XXX,YYY,ZZZ,AAAA,BBBB,CCCC,DDDD,EEEE,FFFF,GGGG renderer
    class I,TTTT,UUUU,VVVV,WWWW,XXXX,YYYY,ZZZZ,AAAAA,BBBBB lifecycle
    class N,GGG,PP,CCCCC,DDDDD,EEEEE error
```

### 核心模块职责

| 模块                        | 职责           | 输入          | 输出          |
| --------------------------- | -------------- | ------------- | ------------- |
| **TranslationOrchestrator** | 流程编排协调   | 页面根节点    | 翻译统计结果  |
| **DomScanner**              | DOM 分析与过滤 | HTMLElement   | ScannedNode[] |
| **TranslateService**        | 翻译逻辑处理   | 文本数组      | 翻译结果数组  |
| **DomRenderer**             | DOM 注入渲染   | 元素+翻译文本 | 渲染结果      |

## 🔄 完整翻译流程调用关系

### 1️⃣ 入口层 (Entry Points)

```typescript
// 主入口流程
entrypoints/content.ts
    ↓ 调用
TranslateManagerAdapter.translatePage()
    ↓ 初始化
TranslationOrchestrator(scanner, renderer, stateManager, lifecycleManager, eventBus)
    ↓ 执行
orchestrator.translatePage(options)
```

### 2️⃣ 核心编排流程 (Orchestrator)

```typescript
TranslationOrchestrator.translatePage() {
    // 1. 状态检查
    if (!stateManager.canStart()) throw Error

    // 2. 设置状态
    stateManager.setState(TranslationState.SCANNING)

    // 3. 性能预检
    await performPreflightCheck()

    // 4. 扫描页面
    const scanResult = await scanPage(options)

    // 5. 执行翻译流程
    const translationResult = await executeTranslationFlow(scanResult.nodes)

    // 6. 切换显示模式
    viewController.setViewMode(ViewMode.DUAL)

    // 7. 完成状态
    stateManager.setState(TranslationState.COMPLETED)
}
```

**流程选择策略：**

```typescript
executeTranslationFlow(nodes) {
    if (enableLazyLoading && nodes.length > 50) {
        return executeLazyTranslation(nodes)  // 大页面懒加载
    } else {
        return executeBatchTranslation(nodes)  // 小页面批量处理
    }
}
```

## 🔍 扫描阶段详细分析

### 3️⃣ DomScanner 工作原理

```typescript
DomScanner.scan(config: ScanConfig): {
    nodes: ScannedNode[];
    stats: ScanStats;
}
```

**详细扫描流程：**

1. **基础分析** - `elementAnalyzer.analyzeElements(rootNode)`

   ```typescript
   // 使用 DomElementAnalyzer 进行DOM遍历
   const analysisResult = this.elementAnalyzer.analyzeElements(rootNode);
   ```

2. **格式转换** - `convertToScannedNode()`

   ```typescript
   // 转换为标准 ScannedNode 格式
   scannedNodes = analysisResult.elements.map((element, index) =>
     this.convertToScannedNode(element, index)
   );
   ```

3. **过滤规则应用** - `applyFilters()`

   - 排除选择器过滤
   - 最小文本长度过滤
   - 最大深度过滤

4. **智能冲突处理** - `filterParentChildConflicts()`

   ```typescript
   // 🔧 关键优化：区分"原子内容块"与"容器元素"
   const atomicContentBlocks = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li', ...]

   if (atomicContentBlocks.has(currentTag)) {
     return false  // 原子块不检查子元素
   }
   ```

5. **优先级排序** - `prioritizeNodes()`
   ```typescript
   // 优先级计算规则
   if (["h1", "h2", "h3"].includes(tagName)) return TaskPriority.CRITICAL;
   if (isVisible) return TaskPriority.HIGH;
   if (textLength < 100) return TaskPriority.HIGH;
   return TaskPriority.NORMAL;
   ```

### 🔍 ScannedNode 数据格式

```typescript
interface ScannedNode {
  element: HTMLElement; // DOM元素引用
  text: string; // 提取的纯文本内容
  htmlContent?: string; // HTML内容（保留结构）
  hasHtmlStructure: boolean; // 是否包含HTML结构
  priority: TaskPriority; // 优先级（CRITICAL|HIGH|NORMAL）
  position: number; // 在页面中的位置索引
  links: LinkInfo[]; // 链接信息数组
  metadata: {
    tagName: string; // 标签名
    className?: string; // 类名
    textLength: number; // 文本长度
    isVisible: boolean; // 是否可见
  };
}
```

**扫描统计结果：**

```typescript
interface ScanStats {
  totalScanned: number; // 总扫描节点数
  translatableFound: number; // 发现的可翻译节点数
  filtered: number; // 被过滤掉的节点数
  finalCount: number; // 最终节点数
  duration: number; // 扫描耗时(ms)
}
```

## 📡 翻译阶段详细分析

### 4️⃣ 翻译引擎调用链

```typescript
// 完整调用链路
Orchestrator.translateText(text, format)
    ↓
TranslateService.translateText(text, options)
    ↓
TranslateService.translateTexts([text], options)
    ↓
TranslateEngineManager.translate(texts, options)
    ↓
[按优先级尝试] GoogleTranslateEngine.translateBatch(texts, options)
    ↓
engine.buildGoogleApi1Request() | engine.buildGoogleApi2Request()
    ↓
HTTP Request → Google Translate API
    ↓
engine.parseGoogleApi1Response() | engine.parseGoogleApi2Response()
```

### 🔧 翻译服务详细流程

```typescript
TranslateService.translateTexts(texts, options) {
    // 1. 输入验证
    if (!texts || texts.length === 0) throw Error

    // 2. 强制纯文本格式
    const mergedOptions = { ...options, format: 'text' }

    // 3. 文本清理
    const cleanedTexts = texts.map(text => this.ensurePlainText(text))

    // 4. 缓存检查
    const cachedResults = []
    const uncachedTexts = []
    for (text of cleanedTexts) {
        const cached = await translationCache.get(text, from, to, engine)
        if (cached) cachedResults.push(cached)
        else uncachedTexts.push(text)
    }

    // 5. 批量翻译未缓存的文本
    if (uncachedTexts.length > 0) {
        const response = await engineManager.translate(uncachedTexts, options)
        // 缓存新结果
        await translationCache.set(...)
    }

    // 6. 返回完整结果
    return cachedResults
}
```

### 🌐 引擎降级机制

```typescript
TranslateEngineManager.translate(texts, options) {
    const enginePriority = options.engine
      ? [options.engine]  // 指定引擎
      : this.getEnginesByPriority()  // 默认优先级

    // 按优先级尝试每个引擎
    for (const engineName of enginePriority) {
        try {
            const response = await engine.translateBatch(texts, options)
            return { ...response, engine: engineName, duration }
        } catch (error) {
            // 引擎降级逻辑
            if (options.engine) break  // 指定引擎失败不降级
            if (!this.config.enableFallback) break  // 禁用降级
            if (this.shouldSkipFallback(error)) break  // 特定错误不降级
            continue  // 尝试下一个引擎
        }
    }
}
```

### 📊 翻译响应格式

```typescript
interface TranslateResponse {
  translations: string[]; // 翻译结果数组
  success: boolean; // 翻译是否成功
  engine: string; // 实际使用的引擎
  duration: number; // 翻译耗时(ms)
  error?: string; // 错误信息
}
```

## 🎨 渲染阶段详细分析

### 5️⃣ DomRenderer 工作原理

```typescript
DomRenderer.render(element, translation, config): Promise<RenderResult>
```

**渲染流程步骤：**

1. **智能注入策略选择**

   ```typescript
   if (this.isSmartInjectionEnabled) {
     const strategy = this.ruleEngine.selectStrategy(element, config);
     // 根据injection-rules.json规则选择策略
   }
   ```

2. **注入器初始化**

   ```typescript
   if (this.isSmartInjectionEnabled) {
     this.injector = new EnhancedDOMInjector(); // 智能注入器
   } else {
     this.injector = new DOMInjector(); // 基础注入器
   }
   ```

3. **内容格式处理**

   ```typescript
   const processedContent =
     config.format === "html"
       ? this.processHtmlContent(translation, config.originalLinks)
       : this.processTextContent(translation);
   ```

4. **注入执行**
   ```typescript
   const result = await this.injector.injectTranslation(
     element,
     processedContent,
     injectionOptions
   );
   ```

### 🎯 注入策略详解

根据 `src/config/injection-rules.json` 配置：

| 策略类型            | 适用场景     | 示例元素                  |
| ------------------- | ------------ | ------------------------- |
| **inline**          | 短文本、链接 | `<span>`, `<a>`, 导航菜单 |
| **block**           | 段落、长文本 | `<p>`, `<li>`, `<div>`    |
| **beside**          | 标题元素     | `<h1>` - `<h6>`           |
| **hidden-preserve** | 隐藏内容     | `display:none` 元素       |
| **skip**            | 跳过翻译     | `<code>`, `<pre>`         |

**策略选择逻辑：**

```typescript
// 示例：标题元素使用 beside 策略
{
  "id": "block-headings",
  "condition": {
    "tagName": ["h1", "h2", "h3", "h4", "h5", "h6"]
  },
  "strategy": "beside",
  "priority": 10
}
```

### 🔧 渲染结果格式

```typescript
interface RenderResult {
  success: boolean; // 渲染是否成功
  injectionStrategy?: string; // 使用的注入策略
  duration: number; // 渲染耗时(ms)
  error?: string; // 错误信息
  metadata?: {
    elementsModified: number; // 修改的元素数量
    injectionMethod: string; // 注入方法
  };
}
```

## ⚡ 并行处理与性能优化

### 6️⃣ 生命周期管理器 (LifecycleManager)

```typescript
LifecycleManager {
  // 核心组件
  private concurrencyController: TranslationConcurrencyController
  private lazyLoadManager?: LazyLoadManager
  private performanceOptimizer?: PerformanceOptimizer
}
```

### 🎯 并行处理策略

#### **小页面批量处理** (≤50 节点)

```typescript
executeBatchTranslation(nodes) {
  for (const node of nodes) {
    // 顺序处理，确保稳定性
    const translation = await translateText(node.text)
    const renderResult = await renderer.render(element, translation)
  }
}
```

#### **大页面懒加载处理** (>50 节点)

```typescript
executeLazyTranslation(nodes) {
  // 1. 按可见性分类
  const { visibleElements, invisibleElements } =
    lifecycleManager.categorizeElementsByVisibility(elements)

  // 2. 优先处理可见元素
  await processBatch(visibleElements, TaskPriority.HIGH)

  // 3. 懒加载处理不可见元素
  lazyLoadManager.observeElements(invisibleElements)
}
```

### 🔄 并发控制配置

```typescript
// 并发限制参数
TranslationConcurrencyController({
  maxConcurrent: 8, // 最大并发翻译数
  rateLimitPerSecond: 8, // 每秒请求限制
  debug: false,
});

// 懒加载配置
LazyLoadManager({
  rootMargin: "100px", // 提前加载边距
  threshold: [0, 0.1, 0.5], // 可见性阈值
  batchSize: 3, // 批处理大小
  debug: false,
});
```

### 📊 性能监控

```typescript
PerformanceOptimizer({
  enableMemoryMonitoring: true,
  memoryWarningThreshold: 80, // 内存警告阈值(MB)
  memoryCleanupThreshold: 120, // 内存清理阈值(MB)
  enableRenderOptimization: true,
  debug: false,
});
```

## 📋 新旧版本架构对比

### 7️⃣ 旧版本问题总结

**已清理的废弃文件：**

- ❌ `src/content/translate-manager.legacy.ts` (1647 行)
- ❌ `src/features/translate/unified-translate-manager.ts` (863 行)
- ❌ `src/content/scanner.ts` → `scanner.legacy.ts` (保留作参考)

**旧版本主要问题：**

| 问题类型     | 具体问题                   | 影响                 |
| ------------ | -------------------------- | -------------------- |
| **架构混乱** | 3 个重复翻译管理器         | 维护困难、职责不清   |
| **性能瓶颈** | `cloneNode(true)` 克隆 DOM | 严重卡顿、内存占用高 |
| **全页扫描** | 一次性处理所有节点         | 长页面响应慢         |
| **调试陷阱** | Mock 延迟 1 秒             | 用户体验差           |
| **错误处理** | 静默回退到 Mock            | 用户无感知失败       |

### 8️⃣ 新版本架构优势

**清晰的分层架构：**

```
TranslateManagerAdapter (编排层)
├── DomScanner (扫描层) - 专职DOM分析
├── TranslateService (服务层) - 专职翻译逻辑
└── DomRenderer (渲染层) - 专职DOM操作
```

**关键优化对比：**

| 优化项目     | 旧版本          | 新版本          | 性能提升       |
| ------------ | --------------- | --------------- | -------------- |
| **DOM 操作** | cloneNode(true) | 轻量元素创建    | **80%↓ 内存**  |
| **页面扫描** | 全页一次性      | 智能分批+懒加载 | **5x 初始化**  |
| **并发控制** | 无限制          | 8 并发+速率限制 | **稳定性 ↑**   |
| **缓存机制** | 基础缓存        | 智能缓存+清理   | **重复翻译 ↓** |
| **错误处理** | 静默失败        | 用户友好提示    | **体验 ↑**     |

## 🛠️ 关键技术细节

### 9️⃣ 数据流转格式示例

**Scanner → Orchestrator 数据流：**

```typescript
// 输入：HTML页面
<h2 class="title">Game on</h2>
<p>Hello <a href="/link">world</a>!</p>

// 输出：ScannedNode[]
[
  {
    element: h2Element,
    text: "Game on",
    hasHtmlStructure: false,
    links: [],
    priority: TaskPriority.CRITICAL,
    metadata: { tagName: "h2", textLength: 7, isVisible: true }
  },
  {
    element: pElement,
    text: "Hello world!",
    htmlContent: "Hello <a href=\"/link\">world</a>!",
    hasHtmlStructure: true,
    links: [{ href: "/link", text: "world", attributes: {...} }],
    priority: TaskPriority.HIGH,
    metadata: { tagName: "p", textLength: 12, isVisible: true }
  }
]
```

**Orchestrator → TranslateService 数据流：**

```typescript
// 输入：文本数组
texts: ["Game on", "Hello world!"]
options: {
  from: "en",
  to: "zh",
  format: "text",  // 强制纯文本
  engine: "google"
}

// 输出：翻译结果
["开始游戏", "你好世界！"]
```

**TranslateService → Renderer 数据流：**

```typescript
// 输入：元素+翻译+配置
element: h2Element
translation: "开始游戏"
config: {
  language: "zh",
  format: "text",
  enableAccessibility: true,
  originalLinks: []
}

// 输出：渲染结果
{
  success: true,
  injectionStrategy: "beside",
  duration: 15,
  metadata: {
    elementsModified: 1,
    injectionMethod: "enhanced-injection"
  }
}
```

### 🔟 状态管理流程

**翻译状态流转：**

```typescript
enum TranslationState {
  IDLE = 'idle',                  // 空闲状态
  SCANNING = 'scanning',          // 扫描中
  TRANSLATING = 'translating',    // 翻译中
  RENDERING = 'rendering',        // 渲染中
  COMPLETED = 'completed',        // 完成
  ERROR = 'error'                 // 错误
}

// 状态流转顺序
IDLE → SCANNING → TRANSLATING → RENDERING → COMPLETED
  ↓                                              ↑
ERROR ←―――――――――――――――――――――――――――――――――――――――――――――――┘
```

**事件系统：**

```typescript
TranslationEventBus.emit("progress", {
  type: "translation",
  current: 15,
  total: 50,
  percentage: 30,
  message: "正在翻译第15个节点...",
});
```

## 🔍 调试与监控

### 调试模式配置

```typescript
// 开发环境调试
const orchestrator = new TranslationOrchestrator(
  scanner,
  renderer,
  stateManager,
  lifecycleManager,
  eventBus,
  {
    debug: true, // 启用详细日志
    targetLanguage: "zh",
    enableLazyLoading: true,
    enablePerformanceOptimization: true,
  }
);
```

### 关键调试信息

```typescript
// Scanner调试输出
console.log("DOM scan completed", {
  totalScanned: 245,
  translatableFound: 58,
  filtered: 12,
  finalCount: 46,
  duration: "23.45ms",
});

// Translator调试输出
console.log("Translation completed", {
  engine: "google",
  texts: 15,
  cached: 8,
  newTranslations: 7,
  duration: "856ms",
});

// Renderer调试输出
console.log("Render completed", {
  strategy: "beside",
  success: true,
  duration: "12ms",
});
```

## 📚 相关文档

- [注入规则配置](./dom注入.md) - DOM 注入策略详解
- [API 接口文档](./api.md) - 外部 API 接口说明
- [任务规划](./task.md) - 功能开发任务
- [项目规划](./plan.md) - 整体项目规划

## 🚀 最佳实践建议

### 生产环境配置

```typescript
// ✅ 推荐的生产环境配置
const adapter = new TranslateManagerAdapter({
  debug: false, // 生产环境必须关闭
  targetLanguage: "zh",
  enableLazyLoading: true, // 大页面必须开启
  enablePerformanceOptimization: true,
  concurrency: 6, // 适度并发
  onError: userFriendlyErrorHandler,
});
```

### 性能优化建议

1. **大页面** (>100 节点) - 启用懒加载
2. **移动设备** - 降低并发数到 4-6
3. **弱网环境** - 启用更激进的缓存策略
4. **生产环境** - 关闭所有调试日志

---

> **文档版本**: 1.0  
> **最后更新**: 2025-01-26  
> **维护者**: Translation System Team
