# Google Translate API

```bash
curl 'https://translate-pa.googleapis.com/v1/translateHtml' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'content-type: application/json+protobuf' \
  -H 'origin: chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: none' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-goog-api-key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520' \
  --data-raw '[[["If you have a custom development environment, we find that creating a “one click” way to install Claude Code is key to growing adoption across an organization. ","Encourage new users to try <PERSON> for codebase Q&A, or on smaller bug fixes or feature requests. Ask <PERSON> to make a plan. Check <PERSON>’s suggestions and give feedback if it’s off-track. Over time, as users understand this new paradigm better, then they’ll be more effective at letting Claude Code run more agentically. "],"en","zh-CN"],"te_lib"]'
```

# Google API2

```bash
curl 'https://translate.googleapis.com/translate_a/t?client=gtx&dt=t&sl=en&tl=zh-CN&format=html' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -H 'origin: chrome-extension://mjdbhokoopacimoekfgkcoogikbfgngb' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: none' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw 'q=Claude%20Code%20on%20Google%20Vertex%20AI&q=%E2%80%8B&q=Prerequisites&q=Before%20configuring%20Claude%20Code%20with%20Vertex%20AI%2C%20ensure%20you%20have%3A&q=A%20Google%20Cloud%20Platform%20(GCP)%20account%20with%20billing%20enabled&q=A%20GCP%20project%20with%20Vertex%20AI%20API%20enabled&q=Access%20to%20desired%20Claude%20models%20(e.g.%2C%20Claude%20Sonnet%204)&q=Google%20Cloud%20SDK%20(gcloud)%20installed%20and%20configured&q=Quota%20allocated%20in%20desired%20GCP%20region&q=Vertex%20AI%20may%20not%20support%20the%20Claude%20Code%20default%20models%20on%20non-us-east5regions.%20Ensure%20you%20are%20using%20us-east5%20and%20have%20quota%20allocated%2C%20or%20switch%20to%20supported%20models.&q=%E2%80%8B&q=Setup&q=%E2%80%8B&q=1.%20Enable%20Vertex%20AI%20API&q=Enable%20the%20Vertex%20AI%20API%20in%20your%20GCP%20project%3A&q=%E2%80%8B&q=2.%20Request%20model%20access&q=Request%20access%20to%20Claude%20models%20in%20Vertex%20AI%3A&q=Navigate%20to%20the%20Vertex%20AI%20Model%20Garden&q=Search%20for%20%E2%80%9CClaude%E2%80%9D%20models'
```

# Microsoft Translate API

```bash
curl 'https://edge.microsoft.com/translate/auth' \
  -H 'accept: */*' \
  -H 'accept-language: zh-TW,zh;q=0.9,en-US;q=0.6,en;q=0.5' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: none' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

  return
  eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-7suQuK_BhuzWh8Pq9Hjw_SW5Fi3JLbbSoVtJp7T03MQyN03-Y1eB_CryX4NT9B7Dni4by2EQRN8oNEI3k-4lw
```

```bash
curl 'https://api-edge.cognitive.microsofttranslator.com/translate?from=en&to=zh-Hans&api-version=3.0' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'authorization: Bearer eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Saq1MxjmY4A1EjSDstmeHjN-trFTIwDjcRGaHCTlBUn9-29jUm07vn1cKm4GtAXaRVMB0iZ4ZziljoldOYiFwA' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'origin: chrome-extension://mjdbhokoopacimoekfgkcoogikbfgngb' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: none' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '[{"Text":"Request access to desired Claude models (e.g., Claude Sonnet 4)"},{"Text":"Wait for approval (may take 24-48 hours)"},{"Text":"3. Configure GCP credentials"},{"Text":"Claude Code uses standard Google Cloud authentication."},{"Text":"For more information, see Google Cloud authentication documentation."},{"Text":"4. Configure Claude Code"},{"Text":"Set the following environment variables:"},{"Text":"Prompt caching is automatically supported when you specify the cache_control ephemeral flag. To disable it, set DISABLE_PROMPT_CACHING=1. For heightened rate limits, contact Google Cloud support."},{"Text":"3. Configure GCP credentials"},{"Text":"4. Configure Claude Code"},{"Text":"5. Model configuration"},{"Text":"IAM configuration"},{"Text":"Troubleshooting"}]'

```
