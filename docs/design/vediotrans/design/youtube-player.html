<div data-layer="variant=1,:hover=false" class="Variant1HoverFalse" style="width: 264px; height: 32px; padding-left: 9px; padding-right: 9px; padding-top: 1px; padding-bottom: 1px; position: relative; background: var(--color-white--9%, rgba(255, 255, 255, 0.09)); border-radius: 6px; outline: 1px var(--color-white--12%, rgba(255, 255, 255, 0.12)) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 4px; display: inline-flex">
  <div data-layer="Container" class="Container" style="width: 30px; height: 30px; left: 233px; top: 1px; position: absolute; justify-content: center; align-items: center; display: flex">
    <div data-svg-wrapper data-layer="Component 1" data-variant="26" class="Component1" style="position: relative">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.1427 14.0557C4.69092 14.0557 1.8927 11.2574 1.8927 7.80566C1.8927 4.35388 4.69092 1.55566 8.1427 1.55566C11.5944 1.55566 14.3927 4.35388 14.3927 7.80566C14.3927 11.2574 11.5944 14.0557 8.1427 14.0557ZM8.1427 12.8057C10.9041 12.8057 13.1427 10.5671 13.1427 7.80566C13.1427 5.04424 10.9041 2.80566 8.1427 2.80566C5.38128 2.80566 3.1427 5.04424 3.1427 7.80566C3.1427 10.5671 5.38128 12.8057 8.1427 12.8057ZM7.5177 4.68066H8.7677V5.93066H7.5177V4.68066ZM7.5177 7.18066H8.7677V10.9307H7.5177V7.18066Z" fill="var(--color-white--50-, white)" fill-opacity="0.5"/>
      </svg>
    </div>
  </div>
  <div data-layer="Container" class="Container" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-svg-wrapper data-layer="Component 1" data-variant="27" class="Component1" style="position: relative">
      <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M2.41251 4.15996C2.64017 3.8921 2.95165 3.8004 3.2252 3.80589C4.18916 3.82516 7.04098 4.16362 10.2557 5.43939C11.9651 4.84359 14.228 4.36101 16.973 4.33166C17.6249 4.32469 18.1427 4.86111 18.1427 5.50725V11.6338C18.1427 12.2915 17.6179 12.806 16.9912 12.8178C16.1028 12.8345 15.0971 12.9202 14.2668 13.017V15.766C14.2668 15.8781 14.2529 16.0044 14.2133 16.1349C13.9219 17.0951 13.192 17.7565 12.3809 18.1694C11.5683 18.5832 10.6071 18.7861 9.69576 18.8043C8.79031 18.8225 7.85973 18.6595 7.12482 18.2661C6.37618 17.8653 5.74709 17.1652 5.74709 16.1485V16.1447L5.78207 12.5361C5.58685 12.4671 5.33659 12.387 5.05796 12.3056C4.3598 12.1015 3.57024 11.9141 3.10126 11.8639C2.61818 11.8122 2.16115 11.4094 2.15358 10.8248C2.13128 9.10165 2.14915 5.88648 2.15547 4.89808C2.1568 4.69232 2.20439 4.40484 2.41251 4.15996ZM7.31368 12.0389L7.29812 13.6711C8.57597 12.6335 10.5853 11.9608 13.3992 11.5684C14.2568 11.4488 15.4853 11.3205 16.6157 11.2804V5.88576C14.0692 5.9544 12.0085 6.43714 10.5005 6.99269C8.95798 7.56099 7.95193 8.29414 7.46354 8.81452C7.35689 8.92814 7.27805 9.13784 7.2823 9.48343L7.31368 12.0389ZM7.35082 16.0589C7.30021 16.1802 7.28116 16.3179 7.33772 16.4366C7.41468 16.5982 7.56868 16.7539 7.83811 16.8981C8.27881 17.134 8.93326 17.2722 9.66559 17.2575C10.392 17.2429 11.1214 17.0792 11.6952 16.787C12.1722 16.5442 12.4961 16.2388 12.6672 15.8966C12.723 15.7852 12.7399 15.6597 12.7399 15.5351V13.236C9.25555 13.8383 7.84692 14.8689 7.35082 16.0589ZM5.77269 10.9067L5.75546 9.50266C5.74882 8.9621 5.86091 8.27696 6.35698 7.74837C6.77563 7.30231 7.39811 6.80926 8.21465 6.34848C6.25519 5.71971 4.58365 5.46218 3.67957 5.38203C3.67288 6.5696 3.66305 8.86058 3.67599 10.3848C4.23544 10.4815 4.9139 10.6532 5.48113 10.819C5.58051 10.8481 5.6781 10.8774 5.77269 10.9067Z" fill="var(--color-violet-66, #8052FF)"/>
      </svg>
    </div>
  </div>
  <div data-layer="Container" class="Container" style="justify-content: flex-start; align-items: center; display: flex">
    <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--color-white--80%, rgba(255, 255, 255, 0.80)); font-size: 14px; font-family: Roboto; font-weight: 400; line-height: 14px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">学习模式</div>
  </div>
</div>