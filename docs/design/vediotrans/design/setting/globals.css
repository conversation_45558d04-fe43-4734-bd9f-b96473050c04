@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400&display=swap");

* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  height: 100%;
}

button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}

a {
  text-decoration: none;
}

select:focus {
  outline: none;
}
.overlay-border {
  display: flex;
  flex-direction: column;
  width: 282px;
  align-items: flex-start;
  padding: 1px;
  position: relative;
  background-color: #1c1c1ce6;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid;
  border-color: #ffffff1a;
}

.overlay-border .container {
  display: flex;
  width: 560px;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
  margin-right: -280.00px;
}

.overlay-border .div {
  display: flex;
  flex-direction: column;
  width: 280px;
  align-items: center;
  padding: 0px 0px 4px;
  position: relative;
  align-self: stretch;
}

.overlay-border .horizontal-border {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 0px 1px;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: #ffffff14;
}

.overlay-border .component {
  display: flex;
  height: 40px;
  align-items: center;
  padding: 0px 8px 0px 12px;
  position: relative;
  align-self: stretch;
  width: 100%;
}

.overlay-border .margin {
  display: flex;
  width: 28px;
  height: 20px;
  padding: 0px 8px 0px 0px;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.overlay-border .component-wrapper {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  position: relative;
}

.overlay-border .img {
  position: relative;
  width: 20px;
  height: 20px;
}

.overlay-border .container-2 {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .text-wrapper {
  position: relative;
  width: fit-content;
  margin-top: -0.50px;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  color: #ffffffcc;
  font-size: 12.9px;
  letter-spacing: 0;
  line-height: 13px;
  white-space: nowrap;
}

.overlay-border .img-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .component-2 {
  position: relative;
  width: 14px;
  height: 14px;
}

.overlay-border .container-wrapper {
  display: flex;
  width: 149.97px;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  margin-right: -3.97px;
}

.overlay-border .container-3 {
  display: flex;
  width: 56px;
  height: 38px;
  align-items: flex-start;
  justify-content: center;
  padding: 12px;
  position: relative;
  overflow: hidden;
}

.overlay-border .background {
  flex: 1;
  align-self: stretch;
  flex-grow: 1;
  border-radius: 7px;
  opacity: 0.5;
  position: relative;
  background-color: #ffc862;
}

.overlay-border .background-shadow-wrapper {
  display: inline-flex;
  height: 38px;
  align-items: center;
  justify-content: center;
  padding: 10px;
  position: absolute;
  top: 0;
  left: 20px;
  border-radius: 19px;
}

.overlay-border .background-shadow {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  box-shadow: 0px 1px 3px #0000001f, 0px 1px 1px #00000024, 0px 2px 1px -1px
    #00000033;
  position: relative;
  background-color: #ffc862;
}

.overlay-border .container-4 {
  display: inline-flex;
  align-items: center;
  gap: 3.99px;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .text-wrapper-2 {
  position: relative;
  width: fit-content;
  margin-top: -0.50px;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  color: #ffffffcc;
  font-size: 13px;
  letter-spacing: 0;
  line-height: 13px;
  white-space: nowrap;
}

.overlay-border .div-wrapper {
  display: flex;
  width: 176.1px;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  margin-right: -4.09px;
}

.overlay-border .text-wrapper-3 {
  color: #ffffffcc;
  font-size: 13px;
  line-height: 13px;
  position: relative;
  width: fit-content;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  letter-spacing: 0;
  white-space: nowrap;
}

.overlay-border .container-5 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
  position: relative;
  flex: 1;
  flex-grow: 1;
}

.overlay-border .margin-2 {
  display: inline-flex;
  max-width: 110px;
  padding: 0px 4px 0px 0px;
  flex: 0 0 auto;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.overlay-border .container-6 {
  display: inline-flex;
  flex-direction: column;
  max-width: 106px;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .text-wrapper-4 {
  font-size: 12.9px;
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  color: #ffffffe6;
  letter-spacing: 0;
  line-height: 15.6px;
  white-space: nowrap;
}

.overlay-border .component-3 {
  position: relative;
  width: 18px;
  height: 18px;
}

.overlay-border .container-7 {
  display: inline-flex;
  flex-direction: column;
  min-width: 26px;
  max-width: 106px;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .text-wrapper-5 {
  font-size: 13px;
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  color: #ffffffe6;
  letter-spacing: 0;
  line-height: 15.6px;
  white-space: nowrap;
}

.overlay-border .horizontal-border-2 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 0px 9px;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: #ffffff14;
}

.overlay-border .text-wrapper-6 {
  position: relative;
  width: fit-content;
  margin-top: -1.00px;
  text-shadow: 0px 0px 2px #00000080;
  font-family: "Roboto-Regular", Helvetica;
  font-weight: 400;
  color: #ffffffe6;
  font-size: 12.3px;
  letter-spacing: 0;
  line-height: 15.6px;
  white-space: nowrap;
}

.overlay-border .container-8 {
  display: inline-flex;
  flex-direction: column;
  min-width: 52px;
  max-width: 106px;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
}

.overlay-border .container-9 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  flex: 1;
  flex-grow: 1;
}

.overlay-border .component-4 {
  position: relative;
  width: 19px;
  height: 19px;
}

.overlay-border .component-5 {
  display: flex;
  width: 264px;
  height: 32px;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 1px 9px;
  position: relative;
  background-color: #ffffff17;
  border-radius: 6px;
  border: 1px solid;
  border-color: #ffffff1f;
}

.overlay-border .container-10 {
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 1px;
  left: 233px;
  z-index: 2;
}

.overlay-border .component-6 {
  position: relative;
  width: 15px;
  height: 15px;
}

.overlay-border .container-11 {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
  z-index: 1;
}

.overlay-border .container-12 {
  display: inline-flex;
  align-items: center;
  position: relative;
  flex: 0 0 auto;
  z-index: 0;
}

.overlay-border .text {
  margin-top: -1.00px;
  color: #ffffffcc;
  font-size: var(--roboto-regular-font-size);
  line-height: var(--roboto-regular-line-height);
  position: relative;
  width: fit-content;
  text-shadow: 0px 0px 2px #00000080;
  font-family: var(--roboto-regular-font-family);
  font-weight: var(--roboto-regular-font-weight);
  letter-spacing: var(--roboto-regular-letter-spacing);
  white-space: nowrap;
  font-style: var(--roboto-regular-font-style);
}

.overlay-border .rectangle {
  position: relative;
  align-self: stretch;
  width: 280px;
}
/* Inject original CSS here */

/* Additional styles */
body {
  font-family: "Roboto", sans-serif;
  background-color: #1c1c1c;
  color: #ffffff;
}

.settings-panel {
  display: flex;
  flex-direction: column;
  width: 280px;
}

.settings-group {
  border-bottom: 1px solid #ffffff14;
  padding-bottom: 1px;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 12px 8px 12px 12px;
  height: 40px;
}

.setting-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.setting-label {
  flex-grow: 1;
  font-size: 13px;
  line-height: 13px;
  color: #ffffffcc;
  text-shadow: 0px 0px 2px #00000080;
}

.setting-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  width: 14px;
  height: 14px;
}

.setting-toggle {
  margin-left: auto;
}

.switch {
  position: relative;
  display: inline-block;
  width: 56px;
  height: 38px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffc862;
  opacity: 0.5;
  transition: .4s;
  border-radius: 7px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 10px;
  bottom: 10px;
  background-color: #ffc862;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0px 1px 3px #0000001f, 0px 1px 1px #00000024, 0px 2px 1px -1px
    #00000033;
}

input:checked + .slider {
  background-color: #ffc862;
}

input:checked + .slider:before {
  transform: translateX(18px);
}

.setting-select {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.setting-select select {
  appearance: none;
  background: transparent;
  border: none;
  color: #ffffffe6;
  font-size: 13px;
  padding-right: 20px;
}

.dropdown-icon {
  width: 18px;
  height: 18px;
  position: absolute;
  right: 0;
  pointer-events: none;
}

.setting-action {
  margin-left: auto;
}

.action-icon {
  width: 18px;
  height: 18px;
}

.learning-mode {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff17;
  border: 1px solid #ffffff1f;
  border-radius: 6px;
  padding: 1px 9px;
  margin-top: 9px;
  height: 32px;
}

.learning-mode-icon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

.learning-mode-text {
  font-size: 14px;
  color: #ffffffcc;
  text-shadow: 0px 0px 2px #00000080;
}

.learning-mode .info-icon {
  margin-left: auto;
}

:root {
  --inter-regular-font-family: "Inter", Helvetica;
  --inter-regular-font-weight: 400;
  --inter-regular-font-size: 17px;
  --inter-regular-letter-spacing: 0px;
  --inter-regular-line-height: 23.860000610351562px;
  --inter-regular-font-style: normal;
  --roboto-regular-font-family: "Roboto", Helvetica;
  --roboto-regular-font-weight: 400;
  --roboto-regular-font-size: 14px;
  --roboto-regular-letter-spacing: 0px;
  --roboto-regular-line-height: 14px;
  --roboto-regular-font-style: normal;
}

